"""
Android DOM元素提取工具
自动获取Android手机当前页面的DOM元素，提取所有可点击的元素信息，并以JSON格式输出

功能：
1. 打开指定APP
2. 导航到对应的页面
3. 获取当前页面DOM树
4. 将XML文件下载到本地
5. 解析XML文件
6. 筛选出所有可点击的元素信息
7. 以JSON格式输出

使用方法:
python android_dom_extractor.py --package com.transsion.aivoiceassistant --output output.json
"""

import argparse
import json
import os
import sys
import time
import xml.etree.ElementTree as ET
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

try:
    import uiautomator2 as u2
    from loguru import logger as log
    from .uiautomator2_manager import UIAutomator2Manager
except ImportError as e:
    print(f"导入依赖失败: {e}")
    print("请确保已安装uiautomator2: pip install uiautomator2")
    sys.exit(1)


class AndroidDOMExtractor:
    """Android DOM元素提取器"""
    
    def __init__(self, device_id: Optional[str] = None):
        """
        初始化DOM提取器
        
        Args:
            device_id: 设备ID，为None时使用默认设备
        """
        self.device_id = device_id
        self.device = None
        self.ui_manager = UIAutomator2Manager()
        self.output_dir = Path("tools/output")
        self.temp_dir = Path("temp")
        
        # 确保输出目录存在
        self.output_dir.mkdir(exist_ok=True)
        self.temp_dir.mkdir(exist_ok=True)
        
    def connect_device(self) -> bool:
        """
        连接设备
        
        Returns:
            bool: 连接是否成功
        """
        try:
            log.info(f"正在连接设备: {self.device_id or 'default'}")
            
            # 检查UIAutomator2服务健康状态
            if not self.ui_manager.check_service_health(self.device_id):
                log.warning("UIAutomator2服务不健康，尝试重启...")
                if not self.ui_manager.restart_service(self.device_id):
                    log.error("重启UIAutomator2服务失败")
                    return False
            
            # 连接设备
            if self.device_id:
                self.device = u2.connect(self.device_id)
            else:
                self.device = u2.connect()
            
            # 验证连接
            device_info = self.device.device_info
            log.info(f"设备连接成功: {device_info.get('brand', 'Unknown')} {device_info.get('model', 'Unknown')}")
            return True
            
        except Exception as e:
            log.error(f"连接设备失败: {e}")
            return False
    
    def launch_app(self, package_name: str, wait_time: int = 3) -> bool:
        """
        启动指定APP
        
        Args:
            package_name: APP包名
            wait_time: 等待时间（秒）
            
        Returns:
            bool: 启动是否成功
        """
        try:
            log.info(f"正在启动APP: {package_name}")
            
            # 检查APP是否已安装
            if not self.device.app_info(package_name):
                log.error(f"APP未安装: {package_name}")
                return False
            
            # 启动APP
            self.device.app_start(package_name)
            time.sleep(wait_time)
            
            # 验证APP是否启动成功
            current_app = self.device.app_current()
            if current_app.get('package') == package_name:
                log.info(f"APP启动成功: {package_name}")
                return True
            else:
                log.warning(f"APP可能未完全启动，当前APP: {current_app.get('package')}")
                return True  # 有时候启动需要时间，先返回True
                
        except Exception as e:
            log.error(f"启动APP失败: {e}")
            return False
    
    def get_page_source(self) -> Optional[str]:
        """
        获取当前页面的XML源码
        
        Returns:
            str: XML源码，失败时返回None
        """
        try:
            log.info("正在获取页面DOM树...")
            
            # 获取页面源码
            xml_content = self.device.dump_hierarchy()
            
            if not xml_content:
                log.error("获取页面源码失败")
                return None
            
            log.info("页面DOM树获取成功")
            return xml_content
            
        except Exception as e:
            log.error(f"获取页面源码失败: {e}")
            return None
    
    def save_xml_to_file(self, xml_content: str, filename: Optional[str] = None) -> str:
        """
        将XML内容保存到文件
        
        Args:
            xml_content: XML内容
            filename: 文件名，为None时自动生成
            
        Returns:
            str: 保存的文件路径
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"page_source_{timestamp}.xml"
        
        file_path = self.temp_dir / filename
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(xml_content)
            
            log.info(f"XML文件已保存: {file_path}")
            return str(file_path)
            
        except Exception as e:
            log.error(f"保存XML文件失败: {e}")
            raise
    
    def parse_xml_elements(self, xml_content: str) -> List[Dict[str, Any]]:
        """
        解析XML内容，提取可点击元素信息
        
        Args:
            xml_content: XML内容
            
        Returns:
            List[Dict]: 可点击元素信息列表
        """
        try:
            log.info("正在解析XML元素...")
            
            # 解析XML
            root = ET.fromstring(xml_content)
            clickable_elements = []
            
            # 递归遍历所有元素
            def traverse_element(element, parent_path=""):
                # 构建元素路径
                element_path = f"{parent_path}/{element.tag}" if parent_path else element.tag
                
                # 获取元素属性
                attrs = element.attrib
                
                # 检查是否为可点击元素
                is_clickable = (
                    attrs.get('clickable', 'false').lower() == 'true' or
                    attrs.get('focusable', 'false').lower() == 'true' or
                    attrs.get('long-clickable', 'false').lower() == 'true'
                )
                
                if is_clickable:
                    # 提取元素信息
                    element_info = {
                        'xpath': element_path,
                        'class': attrs.get('class', ''),
                        'resource_id': attrs.get('resource-id', ''),
                        'text': attrs.get('text', ''),
                        'content_desc': attrs.get('content-desc', ''),
                        'bounds': attrs.get('bounds', ''),
                        'clickable': attrs.get('clickable', 'false'),
                        'focusable': attrs.get('focusable', 'false'),
                        'long_clickable': attrs.get('long-clickable', 'false'),
                        'enabled': attrs.get('enabled', 'false'),
                        'selected': attrs.get('selected', 'false'),
                        'checked': attrs.get('checked', 'false'),
                        'package': attrs.get('package', ''),
                        'index': attrs.get('index', ''),
                        'scrollable': attrs.get('scrollable', 'false')
                    }
                    
                    # 解析坐标信息
                    bounds = attrs.get('bounds', '')
                    if bounds:
                        element_info['coordinates'] = self._parse_bounds(bounds)
                    
                    clickable_elements.append(element_info)
                
                # 递归处理子元素
                for child in element:
                    traverse_element(child, element_path)
            
            # 开始遍历
            traverse_element(root)
            
            log.info(f"解析完成，找到 {len(clickable_elements)} 个可点击元素")
            return clickable_elements
            
        except Exception as e:
            log.error(f"解析XML失败: {e}")
            return []
    
    def _parse_bounds(self, bounds_str: str) -> Dict[str, int]:
        """
        解析bounds字符串，提取坐标信息
        
        Args:
            bounds_str: bounds字符串，格式如 "[0,0][100,50]"
            
        Returns:
            Dict: 坐标信息
        """
        try:
            # 移除方括号并分割
            bounds_str = bounds_str.replace('[', '').replace(']', ',')
            coords = [int(x) for x in bounds_str.split(',') if x]
            
            if len(coords) >= 4:
                return {
                    'left': coords[0],
                    'top': coords[1],
                    'right': coords[2],
                    'bottom': coords[3],
                    'center_x': (coords[0] + coords[2]) // 2,
                    'center_y': (coords[1] + coords[3]) // 2,
                    'width': coords[2] - coords[0],
                    'height': coords[3] - coords[1]
                }
        except Exception as e:
            log.warning(f"解析bounds失败: {bounds_str}, 错误: {e}")
        
        return {}
    
    def save_elements_to_json(self, elements: List[Dict[str, Any]], output_file: str) -> bool:
        """
        将元素信息保存为JSON文件
        
        Args:
            elements: 元素信息列表
            output_file: 输出文件路径
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 构建完整的输出数据
            output_data = {
                'timestamp': datetime.now().isoformat(),
                'device_info': self.device.device_info if self.device else {},
                'total_clickable_elements': len(elements),
                'elements': elements
            }
            
            # 保存到文件
            output_path = self.output_dir / output_file
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)
            
            log.info(f"元素信息已保存到: {output_path}")
            return True
            
        except Exception as e:
            log.error(f"保存JSON文件失败: {e}")
            return False

    def extract_dom_elements(self, package_name: str, output_file: str,
                           wait_time: int = 3, save_xml: bool = True) -> bool:
        """
        完整的DOM元素提取流程

        Args:
            package_name: APP包名
            output_file: 输出JSON文件名
            wait_time: 启动APP后等待时间
            save_xml: 是否保存XML文件

        Returns:
            bool: 提取是否成功
        """
        try:
            log.info("开始DOM元素提取流程...")

            # 1. 连接设备
            if not self.connect_device():
                return False

            # 2. 启动APP
            if not self.launch_app(package_name, wait_time):
                return False

            # 3. 获取页面源码
            xml_content = self.get_page_source()
            if not xml_content:
                return False

            # 4. 保存XML文件（可选）
            if save_xml:
                self.save_xml_to_file(xml_content)

            # 5. 解析XML，提取可点击元素
            elements = self.parse_xml_elements(xml_content)
            if not elements:
                log.warning("未找到可点击元素")

            # 6. 保存为JSON格式
            if self.save_elements_to_json(elements, output_file):
                log.info("DOM元素提取完成！")
                return True
            else:
                return False

        except Exception as e:
            log.error(f"DOM元素提取失败: {e}")
            return False

    def get_current_activity(self) -> Optional[str]:
        """
        获取当前Activity信息

        Returns:
            str: 当前Activity名称
        """
        try:
            if self.device:
                current_app = self.device.app_current()
                return current_app.get('activity', '')
        except Exception as e:
            log.warning(f"获取当前Activity失败: {e}")
        return None

    def wait_for_page_load(self, timeout: int = 10) -> bool:
        """
        等待页面加载完成

        Args:
            timeout: 超时时间（秒）

        Returns:
            bool: 页面是否加载完成
        """
        try:
            log.info("等待页面加载...")

            # 简单的等待策略：检查页面是否稳定
            start_time = time.time()
            last_xml = ""
            stable_count = 0

            while time.time() - start_time < timeout:
                current_xml = self.device.dump_hierarchy()
                if current_xml == last_xml:
                    stable_count += 1
                    if stable_count >= 2:  # 连续2次相同认为页面稳定
                        log.info("页面加载完成")
                        return True
                else:
                    stable_count = 0

                last_xml = current_xml
                time.sleep(1)

            log.warning("页面加载超时")
            return False

        except Exception as e:
            log.error(f"等待页面加载失败: {e}")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Android DOM元素提取工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 提取指定APP的可点击元素
  python android_dom_extractor.py --package com.transsion.aivoiceassistant

  # 指定输出文件名
  python android_dom_extractor.py --package com.transsion.aivoiceassistant --output ella_elements.json

  # 指定设备ID
  python android_dom_extractor.py --package com.transsion.aivoiceassistant --device emulator-5554

  # 不保存XML文件
  python android_dom_extractor.py --package com.transsion.aivoiceassistant --no-save-xml
        """
    )

    parser.add_argument(
        '--package', '-p',
        required=True,
        help='APP包名 (例如: com.transsion.aivoiceassistant)'
    )

    parser.add_argument(
        '--output', '-o',
        default=None,
        help='输出JSON文件名 (默认: dom_elements_TIMESTAMP.json)'
    )

    parser.add_argument(
        '--device', '-d',
        default=None,
        help='设备ID (默认: 使用第一个连接的设备)'
    )

    parser.add_argument(
        '--wait-time', '-w',
        type=int,
        default=3,
        help='启动APP后等待时间（秒，默认: 3）'
    )

    parser.add_argument(
        '--no-save-xml',
        action='store_true',
        help='不保存XML文件'
    )

    parser.add_argument(
        '--wait-page-load',
        action='store_true',
        help='等待页面加载完成'
    )

    args = parser.parse_args()

    # 生成默认输出文件名
    if not args.output:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        package_short = args.package.split('.')[-1]  # 取包名最后一部分
        args.output = f"dom_elements_{package_short}_{timestamp}.json"

    # 创建提取器
    extractor = AndroidDOMExtractor(args.device)

    try:
        # 执行提取
        success = extractor.extract_dom_elements(
            package_name=args.package,
            output_file=args.output,
            wait_time=args.wait_time,
            save_xml=not args.no_save_xml
        )

        if success:
            print(f"\n✅ DOM元素提取成功！")
            print(f"📁 输出文件: tools/output/{args.output}")
            if not args.no_save_xml:
                print(f"📄 XML文件已保存到: temp/ 目录")
        else:
            print("\n❌ DOM元素提取失败！")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
