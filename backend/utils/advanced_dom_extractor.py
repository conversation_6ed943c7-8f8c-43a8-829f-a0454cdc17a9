"""
高级Android DOM元素提取工具
扩展版本，支持更多高级功能：
- 元素过滤和分类
- 批量处理多个页面
- 元素截图标注
- 交互性测试
- 自定义输出格式

使用方法:
python tools/advanced_dom_extractor.py --package com.transsion.aivoiceassistant --mode advanced
"""

import argparse
import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

try:
    from .android_dom_extractor import AndroidDOMExtractor
    from core.logger import log
    import uiautomator2 as u2
except ImportError as e:
    print(f"导入失败: {e}")
    sys.exit(1)


class AdvancedDOMExtractor(AndroidDOMExtractor):
    """高级DOM元素提取器"""
    
    def __init__(self, device_id: Optional[str] = None):
        super().__init__(device_id)
        self.screenshots_dir = Path("tools/output/screenshots")
        self.screenshots_dir.mkdir(exist_ok=True)
    
    def filter_elements_by_type(self, elements: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        按类型分类元素
        
        Args:
            elements: 元素列表
            
        Returns:
            Dict: 按类型分类的元素字典
        """
        categorized = {
            'buttons': [],
            'text_views': [],
            'edit_texts': [],
            'image_views': [],
            'checkboxes': [],
            'switches': [],
            'tabs': [],
            'lists': [],
            'others': []
        }
        
        for element in elements:
            class_name = element.get('class', '').lower()
            
            if 'button' in class_name:
                categorized['buttons'].append(element)
            elif 'textview' in class_name:
                categorized['text_views'].append(element)
            elif 'edittext' in class_name:
                categorized['edit_texts'].append(element)
            elif 'imageview' in class_name:
                categorized['image_views'].append(element)
            elif 'checkbox' in class_name:
                categorized['checkboxes'].append(element)
            elif 'switch' in class_name:
                categorized['switches'].append(element)
            elif 'tab' in class_name:
                categorized['tabs'].append(element)
            elif 'list' in class_name or 'recycler' in class_name:
                categorized['lists'].append(element)
            else:
                categorized['others'].append(element)
        
        return categorized
    
    def filter_elements_by_text(self, elements: List[Dict[str, Any]], 
                               keywords: List[str]) -> List[Dict[str, Any]]:
        """
        根据文本关键词过滤元素
        
        Args:
            elements: 元素列表
            keywords: 关键词列表
            
        Returns:
            List: 匹配的元素列表
        """
        filtered = []
        
        for element in elements:
            text = element.get('text', '').lower()
            content_desc = element.get('content_desc', '').lower()
            
            for keyword in keywords:
                if keyword.lower() in text or keyword.lower() in content_desc:
                    filtered.append(element)
                    break
        
        return filtered
    
    def take_screenshot_with_annotations(self, elements: List[Dict[str, Any]], 
                                       filename: Optional[str] = None) -> str:
        """
        截图并标注可点击元素
        
        Args:
            elements: 元素列表
            filename: 文件名
            
        Returns:
            str: 截图文件路径
        """
        try:
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"annotated_screenshot_{timestamp}.png"
            
            screenshot_path = self.screenshots_dir / filename
            
            # 截图
            self.device.screenshot(str(screenshot_path))
            
            # 如果有PIL库，可以添加标注
            try:
                from PIL import Image, ImageDraw, ImageFont
                
                img = Image.open(screenshot_path)
                draw = ImageDraw.Draw(img)
                
                # 为每个可点击元素绘制边框和编号
                for i, element in enumerate(elements):
                    coords = element.get('coordinates', {})
                    if coords:
                        left = coords.get('left', 0)
                        top = coords.get('top', 0)
                        right = coords.get('right', 0)
                        bottom = coords.get('bottom', 0)
                        
                        # 绘制红色边框
                        draw.rectangle([left, top, right, bottom], outline='red', width=2)
                        
                        # 绘制编号
                        draw.text((left, top-20), str(i+1), fill='red')
                
                img.save(screenshot_path)
                log.info(f"标注截图已保存: {screenshot_path}")
                
            except ImportError:
                log.warning("PIL库未安装，无法添加标注")
            
            return str(screenshot_path)
            
        except Exception as e:
            log.error(f"截图失败: {e}")
            return ""
    
    def test_element_interactions(self, elements: List[Dict[str, Any]], 
                                max_tests: int = 5) -> List[Dict[str, Any]]:
        """
        测试元素交互性
        
        Args:
            elements: 元素列表
            max_tests: 最大测试数量
            
        Returns:
            List: 测试结果列表
        """
        test_results = []
        
        for i, element in enumerate(elements[:max_tests]):
            coords = element.get('coordinates', {})
            if not coords:
                continue
            
            center_x = coords.get('center_x', 0)
            center_y = coords.get('center_y', 0)
            
            if center_x <= 0 or center_y <= 0:
                continue
            
            try:
                log.info(f"测试元素 {i+1}: {element.get('text', 'No text')}")
                
                # 记录点击前的页面状态
                before_xml = self.device.dump_hierarchy()
                
                # 执行点击
                self.device.click(center_x, center_y)
                time.sleep(1)
                
                # 记录点击后的页面状态
                after_xml = self.device.dump_hierarchy()
                
                # 判断是否有变化
                has_change = before_xml != after_xml
                
                test_result = {
                    'element_index': i,
                    'element_text': element.get('text', ''),
                    'element_class': element.get('class', ''),
                    'click_coordinates': [center_x, center_y],
                    'interaction_successful': has_change,
                    'timestamp': datetime.now().isoformat()
                }
                
                test_results.append(test_result)
                
                # 如果页面有变化，尝试返回
                if has_change:
                    self.device.press("back")
                    time.sleep(1)
                
            except Exception as e:
                log.error(f"测试元素 {i+1} 失败: {e}")
                test_result = {
                    'element_index': i,
                    'element_text': element.get('text', ''),
                    'element_class': element.get('class', ''),
                    'click_coordinates': [center_x, center_y],
                    'interaction_successful': False,
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
                test_results.append(test_result)
        
        return test_results
    
    def generate_advanced_report(self, elements: List[Dict[str, Any]], 
                               categorized: Dict[str, List[Dict[str, Any]]],
                               test_results: List[Dict[str, Any]] = None,
                               screenshot_path: str = "") -> Dict[str, Any]:
        """
        生成高级分析报告
        
        Args:
            elements: 原始元素列表
            categorized: 分类后的元素
            test_results: 交互测试结果
            screenshot_path: 截图路径
            
        Returns:
            Dict: 分析报告
        """
        report = {
            'timestamp': datetime.now().isoformat(),
            'device_info': self.device.device_info if self.device else {},
            'summary': {
                'total_elements': len(elements),
                'categorized_counts': {k: len(v) for k, v in categorized.items()},
                'screenshot_path': screenshot_path
            },
            'categorized_elements': categorized,
            'analysis': {
                'most_common_class': self._get_most_common_class(elements),
                'elements_with_text': len([e for e in elements if e.get('text')]),
                'elements_with_resource_id': len([e for e in elements if e.get('resource_id')]),
                'interactive_elements': len([e for e in elements if e.get('clickable') == 'true'])
            }
        }
        
        if test_results:
            report['interaction_tests'] = {
                'total_tested': len(test_results),
                'successful_interactions': len([r for r in test_results if r.get('interaction_successful')]),
                'test_results': test_results
            }
        
        return report
    
    def _get_most_common_class(self, elements: List[Dict[str, Any]]) -> str:
        """获取最常见的元素类型"""
        class_counts = {}
        for element in elements:
            class_name = element.get('class', 'Unknown')
            class_counts[class_name] = class_counts.get(class_name, 0) + 1
        
        if class_counts:
            return max(class_counts, key=class_counts.get)
        return "Unknown"
    
    def extract_advanced_dom_elements(self, package_name: str, output_file: str,
                                    enable_screenshot: bool = True,
                                    enable_interaction_test: bool = False,
                                    filter_keywords: List[str] = None) -> bool:
        """
        高级DOM元素提取流程
        
        Args:
            package_name: APP包名
            output_file: 输出文件名
            enable_screenshot: 是否启用截图
            enable_interaction_test: 是否启用交互测试
            filter_keywords: 过滤关键词
            
        Returns:
            bool: 提取是否成功
        """
        try:
            log.info("开始高级DOM元素提取流程...")
            
            # 基础提取流程
            if not self.connect_device():
                return False
            
            if not self.launch_app(package_name):
                return False
            
            xml_content = self.get_page_source()
            if not xml_content:
                return False
            
            # 解析元素
            elements = self.parse_xml_elements(xml_content)
            if not elements:
                log.warning("未找到可点击元素")
                return False
            
            # 元素分类
            categorized = self.filter_elements_by_type(elements)
            
            # 关键词过滤
            filtered_elements = elements
            if filter_keywords:
                filtered_elements = self.filter_elements_by_text(elements, filter_keywords)
                log.info(f"关键词过滤后剩余 {len(filtered_elements)} 个元素")
            
            # 截图标注
            screenshot_path = ""
            if enable_screenshot:
                screenshot_path = self.take_screenshot_with_annotations(filtered_elements)
            
            # 交互测试
            test_results = []
            if enable_interaction_test:
                log.info("开始交互性测试...")
                test_results = self.test_element_interactions(filtered_elements[:3])  # 只测试前3个
            
            # 生成高级报告
            report = self.generate_advanced_report(
                elements, categorized, test_results, screenshot_path
            )
            
            # 保存报告
            output_path = self.output_dir / output_file
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            log.info(f"高级分析报告已保存: {output_path}")
            return True
            
        except Exception as e:
            log.error(f"高级DOM元素提取失败: {e}")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="高级Android DOM元素提取工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 基本高级提取
  python advanced_dom_extractor.py --package com.transsion.aivoiceassistant

  # 启用截图标注
  python advanced_dom_extractor.py --package com.transsion.aivoiceassistant --screenshot

  # 启用交互测试
  python advanced_dom_extractor.py --package com.transsion.aivoiceassistant --interaction-test

  # 关键词过滤
  python advanced_dom_extractor.py --package com.transsion.aivoiceassistant --keywords "开始,设置,确定"
        """
    )

    parser.add_argument(
        '--package', '-p',
        required=True,
        help='APP包名'
    )

    parser.add_argument(
        '--output', '-o',
        default=None,
        help='输出文件名'
    )

    parser.add_argument(
        '--device', '-d',
        default=None,
        help='设备ID'
    )

    parser.add_argument(
        '--screenshot',
        action='store_true',
        help='启用截图标注'
    )

    parser.add_argument(
        '--interaction-test',
        action='store_true',
        help='启用交互性测试'
    )

    parser.add_argument(
        '--keywords', '-k',
        default=None,
        help='过滤关键词，用逗号分隔'
    )

    args = parser.parse_args()

    # 生成默认输出文件名
    if not args.output:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        package_short = args.package.split('.')[-1]
        args.output = f"advanced_dom_report_{package_short}_{timestamp}.json"

    # 处理关键词
    filter_keywords = []
    if args.keywords:
        filter_keywords = [k.strip() for k in args.keywords.split(',')]

    # 创建高级提取器
    extractor = AdvancedDOMExtractor(args.device)

    try:
        print(f"🚀 高级DOM元素提取工具")
        print(f"📱 目标应用: {args.package}")
        print(f"📄 输出文件: {args.output}")
        print(f"📸 截图标注: {'启用' if args.screenshot else '禁用'}")
        print(f"🔄 交互测试: {'启用' if args.interaction_test else '禁用'}")
        if filter_keywords:
            print(f"🔍 过滤关键词: {', '.join(filter_keywords)}")
        print("-" * 50)

        # 执行高级提取
        success = extractor.extract_advanced_dom_elements(
            package_name=args.package,
            output_file=args.output,
            enable_screenshot=args.screenshot,
            enable_interaction_test=args.interaction_test,
            filter_keywords=filter_keywords
        )

        if success:
            print(f"\n✅ 高级DOM分析完成！")
            print(f"📁 报告文件: tools/output/{args.output}")
            if args.screenshot:
                print(f"📸 截图文件: tools/output/screenshots/")
        else:
            print(f"\n❌ 高级DOM分析失败！")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
