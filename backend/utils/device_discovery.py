"""
设备发现工具
自动发现连接的Android设备并获取详细信息
"""
import subprocess
import re
import uiautomator2 as u2
from typing import List, Dict, Any, Optional
from core.logger import log



class DeviceDiscovery:
    """设备发现器"""
    
    def __init__(self):
        """初始化设备发现器"""
        self.discovered_devices = []
    
    def get_connected_devices(self) -> List[str]:
        """
        获取通过ADB连接的设备列表
        
        Returns:
            List[str]: 设备序列号列表
        """
        try:
            result = subprocess.run(
                ["adb", "devices"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if result.returncode != 0:
                log.error(f"ADB命令执行失败: {result.stderr}")
                return []
            
            # 解析adb devices输出
            lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
            devices = []
            
            for line in lines:
                if line.strip() and '\t' in line:
                    parts = line.split('\t')
                    if len(parts) >= 2 and parts[1].strip() == 'device':
                        devices.append(parts[0].strip())
            
            log.info(f"发现 {len(devices)} 个连接的设备: {devices}")
            return devices
            
        except subprocess.TimeoutExpired:
            log.error("ADB命令执行超时")
            return []
        except FileNotFoundError:
            log.error("ADB命令未找到，请确保Android SDK已安装并添加到PATH")
            return []
        except Exception as e:
            log.error(f"获取连接设备失败: {e}")
            return []
    
    def get_device_properties(self, device_id: str) -> Dict[str, str]:
        """
        获取设备属性信息
        
        Args:
            device_id: 设备序列号
            
        Returns:
            Dict[str, str]: 设备属性字典
        """
        properties = {}
        
        # 需要获取的属性列表
        prop_commands = {
            'brand': 'ro.product.brand',
            'model': 'ro.product.model',
            'device': 'ro.product.device',
            'manufacturer': 'ro.product.manufacturer',
            'android_version': 'ro.build.version.release',
            'sdk_version': 'ro.build.version.sdk',
            'build_id': 'ro.build.id',
            'build_display_id': 'ro.build.display.id',
            'product_name': 'ro.product.name',
            'board': 'ro.product.board',
            'hardware': 'ro.hardware',
            'cpu_abi': 'ro.product.cpu.abi',
            'fingerprint': 'ro.build.fingerprint',
            # HiOS相关属性
            'hios_version': 'ro.build.version.hios',
            'hios_display_id': 'ro.hios.build.display.id',
            'custom_version': 'ro.build.version.custom',
            'tecno_version': 'ro.tecno.version',
            'infinix_version': 'ro.infinix.version',
            'itel_version': 'ro.itel.version'
        }
        
        for prop_name, prop_key in prop_commands.items():
            try:
                result = subprocess.run(
                    ["adb", "-s", device_id, "shell", "getprop", prop_key],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                
                if result.returncode == 0:
                    value = result.stdout.strip()
                    if value:
                        properties[prop_name] = value
                        
            except Exception as e:
                log.debug(f"获取属性 {prop_key} 失败: {e}")
        
        return properties
    
    def get_uiautomator2_info(self, device_id: str) -> Dict[str, Any]:
        """
        通过UIAutomator2获取设备信息
        
        Args:
            device_id: 设备序列号
            
        Returns:
            Dict[str, Any]: UIAutomator2设备信息
        """
        try:
            # 连接设备
            device = u2.connect(device_id)
            
            # 获取设备信息
            device_info = device.device_info
            
            # 获取屏幕信息
            window_size = device.window_size()
            
            # 合并信息
            ui_info = {
                'uiautomator2_info': device_info,
                'screen_width': window_size[0],
                'screen_height': window_size[1],
                'screen_resolution': f"{window_size[0]}x{window_size[1]}"
            }
            
            return ui_info
            
        except Exception as e:
            log.warning(f"通过UIAutomator2获取设备信息失败: {e}")
            return {}
    
    def extract_hios_version(self, properties: Dict[str, str]) -> str:
        """
        提取HiOS版本号
        
        Args:
            properties: 设备属性字典
            
        Returns:
            str: HiOS版本号
        """
        # 尝试多种方式获取HiOS版本
        hios_candidates = [
            properties.get('hios_version', ''),
            properties.get('hios_display_id', ''),
            properties.get('custom_version', ''),
            properties.get('build_display_id', ''),
            properties.get('tecno_version', ''),
            properties.get('infinix_version', ''),
            properties.get('itel_version', '')
        ]
        
        for candidate in hios_candidates:
            if candidate:
                # 尝试提取版本号
                # 匹配模式如: HiOS_8.6.0, HiOS 8.6, v8.6.0 等
                patterns = [
                    r'HiOS[_\s]*(\d+\.?\d*\.?\d*)',
                    r'v?(\d+\.\d+\.\d+)',
                    r'(\d+\.\d+)',
                    r'Android\s+(\d+)'
                ]
                
                for pattern in patterns:
                    match = re.search(pattern, candidate, re.IGNORECASE)
                    if match:
                        version = match.group(1)
                        log.info(f"提取到HiOS版本: {version} (来源: {candidate})")
                        return version
        
        # 如果没有找到HiOS版本，使用Android版本
        android_version = properties.get('android_version', '')
        if android_version:
            log.info(f"未找到HiOS版本，使用Android版本: {android_version}")
            return android_version
        
        return "Unknown"
    
    def generate_device_name(self, properties: Dict[str, str]) -> str:
        """
        生成设备名称
        
        Args:
            properties: 设备属性字典
            
        Returns:
            str: 设备名称
        """
        brand = properties.get('brand', '').upper()
        model = properties.get('model', '')
        device = properties.get('device', '')
        
        # 优先使用 brand + model
        if brand and model:
            # 如果model已经包含brand，就不重复
            if brand.lower() in model.lower():
                return model
            else:
                return f"{brand} {model}"
        
        # 其次使用 brand + device
        if brand and device:
            return f"{brand} {device}"
        
        # 最后使用model或device
        if model:
            return model
        if device:
            return device
        
        return "Unknown Device"
    
    def discover_device(self, device_id: str) -> Optional[Dict[str, Any]]:
        """
        发现单个设备的详细信息
        
        Args:
            device_id: 设备序列号
            
        Returns:
            Optional[Dict[str, Any]]: 设备详细信息
        """
        try:
            log.info(f"正在发现设备: {device_id}")
            
            # 获取设备属性
            properties = self.get_device_properties(device_id)
            
            # 获取UIAutomator2信息
            ui_info = self.get_uiautomator2_info(device_id)
            
            # 提取关键信息
            device_name = self.generate_device_name(properties)
            hios_version = self.extract_hios_version(properties)
            
            # 构建设备信息
            device_info = {
                'device_id': device_id,
                'device_name': device_name,
                'platform_version': hios_version,
                'brand': properties.get('brand', ''),
                'model': properties.get('model', ''),
                'manufacturer': properties.get('manufacturer', ''),
                'android_version': properties.get('android_version', ''),
                'sdk_version': properties.get('sdk_version', ''),
                'build_id': properties.get('build_id', ''),
                'hardware': properties.get('hardware', ''),
                'cpu_abi': properties.get('cpu_abi', ''),
                'screen_resolution': ui_info.get('screen_resolution', ''),
                'discovery_time': self._get_current_time(),
                'raw_properties': properties,
                'uiautomator2_info': ui_info.get('uiautomator2_info', {})
            }
            
            log.info(f"设备发现完成: {device_name} (HiOS: {hios_version})")
            return device_info
            
        except Exception as e:
            log.error(f"发现设备 {device_id} 失败: {e}")
            return None
    
    def discover_all_devices(self) -> List[Dict[str, Any]]:
        """
        发现所有连接的设备
        
        Returns:
            List[Dict[str, Any]]: 所有设备信息列表
        """
        log.info("开始发现所有连接的设备...")
        
        # 获取连接的设备列表
        device_ids = self.get_connected_devices()
        
        if not device_ids:
            log.warning("未发现任何连接的设备")
            return []
        
        # 发现每个设备的详细信息
        discovered_devices = []
        for device_id in device_ids:
            device_info = self.discover_device(device_id)
            if device_info:
                discovered_devices.append(device_info)
        
        self.discovered_devices = discovered_devices
        log.info(f"设备发现完成，共发现 {len(discovered_devices)} 个设备")
        
        return discovered_devices
    
    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def print_device_summary(self, devices: List[Dict[str, Any]]) -> None:
        """
        打印设备摘要信息
        
        Args:
            devices: 设备信息列表
        """
        if not devices:
            log.info("没有发现任何设备")
            return
        
        log.info("=" * 80)
        log.info("📱 发现的设备摘要")
        log.info("=" * 80)
        
        for i, device in enumerate(devices, 1):
            log.info(f"\n设备 {i}:")
            log.info(f"  设备ID: {device['device_id']}")
            log.info(f"  设备名称: {device['device_name']}")
            log.info(f"  HiOS版本: {device['platform_version']}")
            log.info(f"  Android版本: {device['android_version']}")
            log.info(f"  品牌: {device['brand']}")
            log.info(f"  型号: {device['model']}")
            log.info(f"  屏幕分辨率: {device['screen_resolution']}")
            log.info(f"  CPU架构: {device['cpu_abi']}")
            log.info(f"  发现时间: {device['discovery_time']}")


# 全局设备发现器实例
device_discovery = DeviceDiscovery()
if __name__ == '__main__':
    device_discovery.discover_all_devices()
